/* Task Form Container */
.task-form {
  background: #ffffff;
  border-radius: 8px;
  padding: 0;
  border: none;
  margin: 0;
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* Form Labels */
.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.required {
  color: #ef4444;
  font-weight: 700;
}

/* Form Inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  background-color: #ffffff;
  color: #374151;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #fefefe;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Textarea Specific */
.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

/* Select Specific */
.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: #374151 !important;
  background-color: #ffffff !important;
}

/* Date input specific styling */
.form-input[type="date"] {
  position: relative;
  background-color: #ffffff !important;
  color: #374151 !important;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: textfield;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
  background-size: 1.2em 1.2em;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  width: 20px;
  height: 20px;
  margin-left: 8px;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Firefox date input styling */
.form-input[type="date"]::-moz-focus-inner {
  border: 0;
}

/* Ensure date input text is visible */
.form-input[type="date"]::-webkit-datetime-edit {
  color: #374151 !important;
}

.form-input[type="date"]::-webkit-datetime-edit-text {
  color: #374151 !important;
}

.form-input[type="date"]::-webkit-datetime-edit-month-field {
  color: #374151 !important;
}

.form-input[type="date"]::-webkit-datetime-edit-day-field {
  color: #374151 !important;
}

.form-input[type="date"]::-webkit-datetime-edit-year-field {
  color: #374151 !important;
}

.form-select option {
  padding: 0.5rem;
  color: #374151 !important;
  background-color: #ffffff !important;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background-color: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .btn {
    width: 100%;
    min-width: unset;
  }
}

@media (max-width: 480px) {
  .form-input,
  .form-select,
  .form-textarea {
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
  }
  
  .form-label {
    font-size: 0.8rem;
  }
  
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }
}

/* Animation for form submission */
.task-form.submitting {
  opacity: 0.7;
  pointer-events: none;
}

.task-form.submitting .btn-primary {
  background-color: #9ca3af;
  border-color: #9ca3af;
}

/* Focus states for accessibility */
.form-input:focus-visible,
.form-select:focus-visible,
.form-textarea:focus-visible,
.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
