/* Task Item Container */
.task-item {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

/* Status-based styling */
.task-item.status-todo {
  border-left: 4px solid #6b7280;
}

.task-item.status-in-progress {
  border-left: 4px solid #f59e0b;
}

.task-item.status-done {
  border-left: 4px solid #10b981;
  opacity: 0.8;
}

.task-item.status-done .task-title {
  text-decoration: line-through;
  color: #6b7280;
}

/* Task Header */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-icon {
  font-size: 1rem;
}

.status-text {
  text-transform: capitalize;
  color: #374151;
}

/* Status-specific colors */
.status-todo .task-status {
  background: #f3f4f6;
  color: #6b7280;
}

.status-in-progress .task-status {
  background: #fef3c7;
  color: #d97706;
}

.status-done .task-status {
  background: #d1fae5;
  color: #059669;
}

/* Due Date */
.task-due-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  background: #e5e7eb;
  color: #6b7280;
}

.task-due-date.today {
  background: #fef3c7;
  color: #d97706;
}

.task-due-date.soon {
  background: #dbeafe;
  color: #2563eb;
}

.task-due-date.overdue {
  background: #fee2e2;
  color: #dc2626;
  animation: pulse 2s infinite;
}

.due-date-icon {
  font-size: 0.875rem;
}

/* Task Content */
.task-content {
  margin-bottom: 1.5rem;
}

.task-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.task-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
  max-height: 4.5em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* Task Actions */
.task-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
}

.btn-icon {
  font-size: 0.875rem;
}

.btn-edit {
  background: #dbeafe;
  color: #2563eb;
  border-color: #bfdbfe;
}

.btn-edit:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-delete {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
}

.btn-delete:hover {
  background: #ef4444;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Error State */
.task-item-error {
  background: #fee2e2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .task-item {
    padding: 1rem;
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .task-actions {
    justify-content: stretch;
    width: 100%;
  }
  
  .btn {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 480px) {
  .task-item {
    padding: 0.875rem;
  }
  
  .task-title {
    font-size: 1.125rem;
  }
  
  .task-description {
    font-size: 0.8rem;
  }
  
  .task-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .btn {
    width: 100%;
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }
}

/* Focus states for accessibility */
.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading state */
.task-item.loading {
  opacity: 0.6;
  pointer-events: none;
}

.task-item.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
